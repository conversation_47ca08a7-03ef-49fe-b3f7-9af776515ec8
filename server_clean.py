import json
import os
import sys
import logging
import hashlib
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
import yfinance as yf
import finnhub
import requests
from mcp.server.fastmcp import FastMCP
from dotenv import load_dotenv

# Import pandas for data handling
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    pd = None
    PANDAS_AVAILABLE = False

# Import Stockdex for additional financial data
try:
    from stockdex import Ticker as StockdexTicker
    STOCKDEX_AVAILABLE = True
except ImportError:
    StockdexTicker = None
    STOCKDEX_AVAILABLE = False

# Third-party financial data APIs - Intrinio removed as no longer needed

# SEC EDGAR API
try:
    from sec_edgar_api import EdgarClient
    SEC_EDGAR_AVAILABLE = True
except ImportError:
    EdgarClient = None
    SEC_EDGAR_AVAILABLE = False

# pandas and numpy imported for timestamp handling in JSON encoder

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    stream=sys.stderr,
)
logger = logging.getLogger("multi-source-finance-mcp")

# Custom JSON encoder to handle timestamp serialization
class TimestampSafeJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        # Handle pandas Timestamp objects
        if hasattr(obj, 'strftime'):
            return obj.strftime('%Y-%m-%d %H:%M:%S')
        # Handle numpy datetime64 objects
        if hasattr(obj, 'astype') and 'datetime' in str(type(obj)):
            return str(obj)
        # Handle numpy scalar types
        if hasattr(obj, 'item'):
            return obj.item()
        # Handle pandas NaT (Not a Time)
        if str(obj) == 'NaT':
            return None
        # Handle numpy NaN
        if hasattr(obj, '__float__') and str(obj) == 'nan':
            return None
        return super().default(obj)

def safe_json_dumps(data, **kwargs):
    """JSON dumps with timestamp-safe encoding"""
    return json.dumps(data, cls=TimestampSafeJSONEncoder, **kwargs)

# Initialize FastMCP server
mcp = FastMCP("multi-source-finance")

# Load environment variables
load_dotenv()

# Configuration
FINNHUB_KEY = os.getenv("FINNHUB_KEY")
FINANCIAL_DATASETS_API_KEY = os.getenv("FINANCIAL_DATASETS_API_KEY")

# Initialize Finnhub client
finnhub_client = finnhub.Client(api_key=FINNHUB_KEY) if FINNHUB_KEY else None

# Intrinio SDK removed - no longer needed

# Helper functions
async def call_financial_datasets_api(endpoint: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """Make API call to Financial Datasets API."""
    if not FINANCIAL_DATASETS_API_KEY:
        return {"error": "API key not configured"}

    headers = {"X-API-KEY": FINANCIAL_DATASETS_API_KEY}
    url = f"https://api.financialdatasets.ai/{endpoint}"

    try:
        response = requests.get(url, headers=headers, params=params or {})
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        return {"error": str(e)}
    except Exception as e:
        return {"error": f"Unexpected error: {str(e)}"}

def call_stockdex_api(ticker: str, data_type: str, **kwargs) -> Dict[str, Any]:
    """Make API call to Stockdex (Yahoo Finance alternative)."""
    if not STOCKDEX_AVAILABLE:
        return {"error": "Stockdex not available"}

    try:
        stockdex_ticker = StockdexTicker(ticker=ticker)

        if data_type == "quote":
            # Get current price data from Stockdex
            price_data = stockdex_ticker.yahoo_api_price(range='1d', dataGranularity='1d')
            if not price_data.empty:
                latest = price_data.iloc[-1]
                return {
                    "price": float(latest.get('close', 0)),
                    "change": float(latest.get('close', 0) - latest.get('open', 0)),
                    "volume": int(latest.get('volume', 0)),
                    "timestamp": latest.get('timestamp', '')
                }
        elif data_type == "info":
            # Get company summary from Stockdex
            summary = stockdex_ticker.yahoo_web_summary
            if summary is not None and not summary.empty:
                return {
                    "company_name": summary.get('longName', ticker),
                    "sector": summary.get('sector', 'Unknown'),
                    "industry": summary.get('industry', 'Unknown'),
                    "market_cap": summary.get('marketCap', 0),
                    "description": summary.get('longBusinessSummary', '')
                }
        elif data_type == "historical":
            # Get historical data from Stockdx
            period = kwargs.get('period', '1y')
            hist_data = stockdex_ticker.yahoo_api_price(range=period, dataGranularity='1d')
            if not hist_data.empty:
                records = []
                for _, row in hist_data.iterrows():
                    records.append({
                        "date": str(row.get('timestamp', ''))[:10],
                        "open": float(row.get('open', 0)),
                        "high": float(row.get('high', 0)),
                        "low": float(row.get('low', 0)),
                        "close": float(row.get('close', 0)),
                        "volume": int(row.get('volume', 0))
                    })
                return {"data": records}

        return {"error": "No data available"}
    except Exception as e:
        return {"error": str(e)}

def generate_demo_data(symbol: str, data_type: str, source: str, **kwargs) -> Dict[str, Any]:
    """Generate consistent demo data across all sources."""
    seed = int(hashlib.md5(f"{symbol}{source}".encode()).hexdigest()[:8], 16) % 1000

    if data_type == "quote":
        base_prices = {"yahoo_finance": 100, "finnhub": 100.5, "financial_datasets": 101, "stockdex": 99.8}
        base_price = base_prices.get(source, 100) + seed / 10

        return {
            "status": "success",
            "data": {
                "price": round(base_price, 2),
                "change": round((seed % 20 - 10) / 10, 2),
                "percent_change": round((seed % 20 - 10) / 100, 3),
                "volume": (seed % 200 + 100) * 1000,
                "market_cap": (seed % 1000 + 500) * 1000000
            },
            "note": f"Demo data - configure API key for live data"
        }

    elif data_type == "info":
        industries = ["Technology", "Healthcare", "Financial", "Energy", "Consumer"]
        return {
            "status": "success",
            "data": {
                "company_name": f"{symbol} Corporation",
                "sector": industries[seed % len(industries)],
                "industry": f"{industries[seed % len(industries)]} Services",
                "employees": (seed % 50 + 10) * 1000,
                "website": f"https://www.{symbol.lower()}.com"
            },
            "note": f"Demo data - configure API key for live data"
        }

    elif data_type == "financials":
        limit = kwargs.get("limit", 2)
        periods_data = {}
        for i in range(limit):
            year = 2024 - i
            periods_data[f"{year}-12-31"] = {
                "revenue": (seed % 500 + 200) * 1000000 * (1 + i * 0.08),
                "net_income": (seed % 100 + 20) * 1000000 * (1 + i * 0.08),
                "total_assets": (seed % 1000 + 500) * 1000000 * (1 + i * 0.05)
            }

        return {
            "status": "success",
            "data": {
                "periods": len(periods_data),
                "statement_type": kwargs.get("statement_type", "income"),
                "period_type": kwargs.get("period", "annual"),
                "summary_data": periods_data
            },
            "note": f"Demo data - configure API key for live data"
        }

    elif data_type == "historical":
        # Generate demo historical data
        period = kwargs.get("period", "1y")
        from_date = kwargs.get("from_date")
        to_date = kwargs.get("to_date")

        # Calculate number of data points
        period_days = {"1d": 1, "5d": 5, "1mo": 30, "3mo": 90, "6mo": 180, "1y": 365, "2y": 730}
        days = period_days.get(period, 365)

        if from_date and to_date:
            try:
                start = datetime.strptime(from_date, '%Y-%m-%d')
                end = datetime.strptime(to_date, '%Y-%m-%d')
                days = (end - start).days
            except:
                days = 365

        # Generate realistic historical data
        base_price = 100 + seed / 10
        history_records = []
        current_price = base_price

        for i in range(min(days, 252)):  # Limit to ~1 year of trading days
            date = (datetime.now() - timedelta(days=days-i)).strftime('%Y-%m-%d')

            # Simulate realistic price movement
            daily_change = ((seed + i) % 40 - 20) / 100  # -20% to +20% daily change
            current_price += daily_change
            current_price = max(current_price, 1)  # Prevent negative prices

            open_price = current_price * (0.995 + (seed + i) % 10 / 1000)
            high_price = current_price * (1.005 + (seed + i) % 15 / 1000)
            low_price = current_price * (0.995 - (seed + i) % 10 / 1000)

            history_records.append({
                "date": date,
                "open": round(open_price, 2),
                "high": round(high_price, 2),
                "low": round(low_price, 2),
                "close": round(current_price, 2),
                "volume": (seed + i) % 1000000 + 100000
            })

        return {
            "status": "success",
            "data_points": len(history_records),
            "data": history_records,
            "note": f"Demo data - configure API key for live data"
        }

    return {"status": "failed", "error": "Unsupported data type"}


async def get_multi_source_data(symbol: str, data_type: str, **kwargs) -> Dict[str, Any]:
    """Optimized multi-source data fetching with unified error handling."""
    symbol = symbol.upper()
    sources = {}

    # Yahoo Finance - Optimized with single info call
    try:
        ticker = yf.Ticker(symbol)
        info = ticker.info  # Single API call

        if data_type == "quote":
            sources["yahoo_finance"] = {
                "status": "success",
                "data": {
                    "price": info.get("currentPrice"),
                    "previous_close": info.get("previousClose"),
                    "market_cap": info.get("marketCap"),
                    "pe_ratio": info.get("trailingPE"),
                    "volume": info.get("volume"),
                    "day_high": info.get("dayHigh"),
                    "day_low": info.get("dayLow")
                }
            }
        elif data_type == "info":
            sources["yahoo_finance"] = {
                "status": "success",
                "data": {
                    "company_name": info.get("longName"),
                    "sector": info.get("sector"),
                    "industry": info.get("industry"),
                    "employees": info.get("fullTimeEmployees"),
                    "website": info.get("website"),
                    "business_summary": info.get("longBusinessSummary"),
                    "market_cap": info.get("marketCap"),
                    "country": info.get("country")
                }
            }
        elif data_type == "financials":
            statement_type = kwargs.get("statement_type", "income")
            period = kwargs.get("period", "annual")
            limit = kwargs.get("limit", 4)

            # Get the appropriate financial statement from Yahoo Finance
            if statement_type == "income":
                if period == "annual":
                    statements = ticker.financials
                else:
                    statements = ticker.quarterly_financials
            elif statement_type == "balance":
                if period == "annual":
                    statements = ticker.balance_sheet
                else:
                    statements = ticker.quarterly_balance_sheet
            elif statement_type == "cash_flow":
                if period == "annual":
                    statements = ticker.cashflow
                else:
                    statements = ticker.quarterly_cashflow
            else:
                sources["yahoo_finance"] = {"status": "failed", "error": f"Invalid statement type: {statement_type}"}
                statements = None

            if statements is not None and not statements.empty:
                # Process the statements data (simplified version)
                actual_limit = min(limit, len(statements.columns))
                limited_statements = statements.iloc[:, :actual_limit]

                # Convert to a simple format for multi-source comparison
                periods_data = {}
                for i, col in enumerate(limited_statements.columns[:actual_limit]):
                    period_name = str(col)[:10] if hasattr(col, 'strftime') else str(col)
                    periods_data[period_name] = {
                        "total_revenue": limited_statements.iloc[:, i].get("Total Revenue", None),
                        "net_income": limited_statements.iloc[:, i].get("Net Income", None),
                        "total_assets": limited_statements.iloc[:, i].get("Total Assets", None) if statement_type == "balance" else None
                    }

                sources["yahoo_finance"] = {
                    "status": "success",
                    "data": {
                        "periods": len(periods_data),
                        "statement_type": statement_type,
                        "period_type": period,
                        "summary_data": periods_data
                    }
                }
            else:
                sources["yahoo_finance"] = {"status": "failed", "error": f"No {statement_type} statements found"}
    except Exception as e:
        sources["yahoo_finance"] = {"status": "failed", "error": str(e)}
    
    # Finnhub - Optimized with unified demo data
    try:
        if finnhub_client:
            if data_type == "quote":
                quote = finnhub_client.quote(symbol)
                sources["finnhub"] = {
                    "status": "success",
                    "data": {
                        "price": quote.get("c"),
                        "change": quote.get("d"),
                        "percent_change": quote.get("dp"),
                        "high": quote.get("h"),
                        "low": quote.get("l"),
                        "open": quote.get("o"),
                        "previous_close": quote.get("pc")
                    }
                }
            elif data_type == "info":
                profile = finnhub_client.company_profile2(symbol=symbol)
                sources["finnhub"] = {
                    "status": "success",
                    "data": {
                        "company_name": profile.get("name"),
                        "country": profile.get("country"),
                        "exchange": profile.get("exchange"),
                        "market_cap": profile.get("marketCapitalization"),
                        "website": profile.get("weburl"),
                        "logo": profile.get("logo"),
                        "industry": profile.get("finnhubIndustry")
                    }
                }
        else:
            # Use unified demo data generation
            sources["finnhub"] = generate_demo_data(symbol, data_type, "finnhub", **kwargs)

        # Handle financials for Finnhub (both live and demo)
        if data_type == "financials":
                statement_type = kwargs.get("statement_type", "income")
                period = kwargs.get("period", "annual")

                if finnhub_client:
                    # Finnhub financial statements
                    try:
                        if statement_type == "income":
                            financials = finnhub_client.company_basic_financials(symbol, "all")
                            annual_data = financials.get("annual", {})

                            if annual_data:
                                # Extract key metrics from Finnhub format
                                periods_data = {}
                                for metric, values in annual_data.items():
                                    if metric in ["totalRevenue", "netIncome", "totalAssets"]:
                                        for entry in values[:4]:  # Last 4 periods
                                            period_key = entry.get("period", "unknown")
                                            if period_key not in periods_data:
                                                periods_data[period_key] = {}
                                            periods_data[period_key][metric] = entry.get("v")

                                sources["finnhub"] = {
                                    "status": "success",
                                    "data": {
                                        "periods": len(periods_data),
                                        "statement_type": statement_type,
                                        "period_type": period,
                                        "summary_data": periods_data
                                    }
                                }
                            else:
                                sources["finnhub"] = {"status": "failed", "error": "No financial data available"}
                        else:
                            sources["finnhub"] = {"status": "failed", "error": f"Statement type {statement_type} not supported by Finnhub"}
                    except Exception as finn_error:
                        sources["finnhub"] = {"status": "failed", "error": f"Finnhub financials error: {str(finn_error)}"}
                else:
                    # Demo financial data
                    seed = int(hashlib.md5(symbol.encode()).hexdigest()[:8], 16)
                    base_revenue = (seed % 500 + 100) * 1000000

                    periods_data = {}
                    for i in range(2):
                        year = 2024 - i
                        periods_data[f"{year}-12-31"] = {
                            "totalRevenue": base_revenue * (1 + i * 0.1),
                            "netIncome": base_revenue * 0.15 * (1 + i * 0.1),
                            "totalAssets": base_revenue * 2 * (1 + i * 0.1)
                        }

                    sources["finnhub"] = {
                        "status": "success",
                        "data": {
                            "periods": len(periods_data),
                            "statement_type": statement_type,
                            "period_type": period,
                            "summary_data": periods_data
                        },
                        "note": "Demo data - configure FINNHUB_KEY for live data"
                    }
    except Exception as e:
        sources["finnhub"] = {"status": "failed", "error": str(e)}
    
    # Intrinio removed - no longer needed

    # Financial Datasets
    try:
        if FINANCIAL_DATASETS_API_KEY:
            if data_type == "quote":
                # Get current price snapshot
                snapshot_data = await call_financial_datasets_api("prices/snapshot", {"ticker": symbol})
                if "error" not in snapshot_data and snapshot_data.get("snapshot"):
                    snapshot = snapshot_data["snapshot"]
                    sources["financial_datasets"] = {
                        "status": "success",
                        "data": {
                            "price": snapshot.get("price"),
                            "change": snapshot.get("change"),
                            "percent_change": snapshot.get("percent_change"),
                            "volume": snapshot.get("volume"),
                            "market_cap": snapshot.get("market_cap")
                        }
                    }
                else:
                    sources["financial_datasets"] = {"status": "failed", "error": snapshot_data.get("error", "No snapshot data")}
            elif data_type == "info":
                # Get company facts
                facts_data = await call_financial_datasets_api("company/facts", {"ticker": symbol})
                if "error" not in facts_data and facts_data.get("company_facts"):
                    facts = facts_data["company_facts"]
                    sources["financial_datasets"] = {
                        "status": "success",
                        "data": {
                            "company_name": facts.get("name"),
                            "sector": facts.get("sector"),
                            "industry": facts.get("industry"),
                            "employees": facts.get("number_of_employees"),
                            "market_cap": facts.get("market_cap"),
                            "website": facts.get("website_url"),
                            "exchange": facts.get("exchange"),
                            "cik": facts.get("cik")
                        }
                    }
                else:
                    sources["financial_datasets"] = {"status": "failed", "error": facts_data.get("error", "No company facts")}
        else:
            # Use unified demo data generation
            sources["financial_datasets"] = generate_demo_data(symbol, data_type, "financial_datasets", **kwargs)
    except Exception as e:
        sources["financial_datasets"] = {"status": "failed", "error": str(e)}

    # Handle news data type
    if data_type == "news":
        days = kwargs.get("days", 7)
        all_news = []

        # Finnhub news
        try:
            if finnhub_client:
                from_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
                to_date = datetime.now().strftime('%Y-%m-%d')
                news = finnhub_client.company_news(symbol, _from=from_date, to=to_date)

                finnhub_articles = []
                for article in news[:15]:
                    finnhub_articles.append({
                        "headline": article.get("headline"),
                        "summary": article.get("summary"),
                        "url": article.get("url"),
                        "source": article.get("source"),
                        "datetime": datetime.fromtimestamp(article.get("datetime", 0)).isoformat() if article.get("datetime") else None,
                        "data_provider": "finnhub"
                    })
                all_news.extend(finnhub_articles)
                sources["finnhub"] = {"status": "success", "articles": len(finnhub_articles)}
            else:
                sources["finnhub"] = {"status": "failed", "error": "API key not configured"}
        except Exception as e:
            sources["finnhub"] = {"status": "failed", "error": str(e)}

        # Intrinio news removed - no longer needed

        # Financial Datasets news
        try:
            if FINANCIAL_DATASETS_API_KEY:
                # Get company news from Financial Datasets
                news_data = await call_financial_datasets_api("news/company", {"ticker": symbol, "limit": 15})
                if "error" not in news_data and news_data.get("news"):
                    financial_datasets_articles = []
                    for article in news_data["news"]:
                        financial_datasets_articles.append({
                            "headline": article.get("title"),
                            "summary": article.get("summary"),
                            "url": article.get("url"),
                            "source": article.get("source"),
                            "datetime": article.get("published_at"),
                            "data_provider": "financial_datasets"
                        })
                    all_news.extend(financial_datasets_articles)
                    sources["financial_datasets"] = {"status": "success", "articles": len(financial_datasets_articles)}
                else:
                    sources["financial_datasets"] = {"status": "failed", "error": news_data.get("error", "No news data")}
            else:
                sources["financial_datasets"] = {"status": "failed", "error": "API key not configured"}
        except Exception as e:
            sources["financial_datasets"] = {"status": "failed", "error": str(e)}

        # Add aggregated news data
        for source_name, source_data in sources.items():
            if source_data.get("status") == "success":
                source_data["data"] = {"news": [article for article in all_news if article.get("data_provider") == source_name]}

    # Handle financial statements data type
    if data_type == "financials":
        statement_type = kwargs.get("statement_type", "income")
        period = kwargs.get("period", "annual")
        limit = kwargs.get("limit", 4)

        # Financial Datasets financials
        try:
            if FINANCIAL_DATASETS_API_KEY:
                # Map statement types to Financial Datasets endpoints
                fd_statement_map = {
                    "income": "income_statement",
                    "balance": "balance_sheet",
                    "cash_flow": "cash_flow_statement"
                }

                if statement_type in fd_statement_map:
                    endpoint = f"financials/{fd_statement_map[statement_type]}"
                    params = {"ticker": symbol, "period": period, "limit": limit}

                    fd_result = await call_financial_datasets_api(endpoint, params)
                    if "error" not in fd_result and fd_result.get("financials"):
                        sources["financial_datasets"] = {
                            "status": "success",
                            "data": {
                                "statements": fd_result["financials"],
                                "periods": len(fd_result["financials"]),
                                "statement_type": statement_type
                            }
                        }
                    else:
                        sources["financial_datasets"] = {
                            "status": "failed",
                            "error": fd_result.get("error", "No financial data")
                        }
                else:
                    sources["financial_datasets"] = {
                        "status": "failed",
                        "error": f"Unsupported statement type: {statement_type}"
                    }
            else:
                sources["financial_datasets"] = {"status": "failed", "error": "API key not configured"}
        except Exception as e:
            sources["financial_datasets"] = {"status": "failed", "error": str(e)}

    # Stockdex - Additional Yahoo Finance alternative
    try:
        if STOCKDEX_AVAILABLE:
            stockdex_result = call_stockdex_api(symbol, data_type, **kwargs)
            if "error" not in stockdex_result:
                sources["stockdex"] = {
                    "status": "success",
                    "data": stockdex_result
                }
            else:
                sources["stockdex"] = {"status": "failed", "error": stockdex_result["error"]}
        else:
            # Use unified demo data generation
            sources["stockdex"] = generate_demo_data(symbol, data_type, "stockdex", **kwargs)
    except Exception as e:
        sources["stockdex"] = {"status": "failed", "error": str(e)}

    return {
        "symbol": symbol,
        "timestamp": datetime.now().isoformat(),
        "sources": sources,
        "summary": {
            "successful_sources": len([s for s in sources.values() if s.get("status") == "success"]),
            "total_sources": len(sources)
        }
    }


@mcp.tool()
async def get_stock_quote(ticker: str) -> str:
    """Get real-time stock quote from multiple sources with validation.
    
    Args:
        ticker: Stock symbol (e.g., 'AAPL', 'MSFT', 'GOOGL')
    """
    try:
        result = await get_multi_source_data(ticker, "quote")
        
        # Calculate consensus price
        prices = []
        for _, data in result["sources"].items():
            if data.get("status") == "success" and data.get("data", {}).get("price"):
                prices.append(data["data"]["price"])
        
        if prices:
            result["consensus_price"] = round(sum(prices) / len(prices), 2)
        
        return safe_json_dumps(result, indent=2)
    except Exception as e:
        return safe_json_dumps({"error": str(e)}, indent=2)


@mcp.tool()
async def get_company_info(ticker: str) -> str:
    """Get comprehensive company information from multiple sources.
    
    Args:
        ticker: Stock symbol (e.g., 'AAPL', 'MSFT', 'GOOGL')
    """
    try:
        result = await get_multi_source_data(ticker, "info")
        return safe_json_dumps(result, indent=2)
    except Exception as e:
        return safe_json_dumps({"error": str(e)}, indent=2)


@mcp.tool()
async def get_historical_data(
    ticker: str,
    period: str = "1y",
    interval: str = "1d",
    start_date: Optional[str] = None,
    end_date: Optional[str] = None
) -> str:
    """Get historical stock prices from multiple sources (Yahoo Finance, Finnhub, Financial Datasets) with statistics.

    Args:
        ticker: Stock symbol (e.g., 'AAPL', 'MSFT', 'GOOGL')
        period: Time period (1d, 5d, 1mo, 3mo, 6mo, 1y, 2y, 5y, 10y, ytd, max) - used if start_date/end_date not provided
        interval: Data interval (1m, 2m, 5m, 15m, 30m, 60m, 90m, 1h, 1d, 5d, 1wk, 1mo, 3mo)
        start_date: Optional start date in YYYY-MM-DD format (overrides period)
        end_date: Optional end date in YYYY-MM-DD format (overrides period)
    """
    try:
        ticker = ticker.upper()
        sources = {}

        # Determine if using date range or period
        use_date_range = start_date and end_date

        if use_date_range:
            # Validate date format
            try:
                if start_date and end_date:
                    datetime.strptime(start_date, '%Y-%m-%d')
                    datetime.strptime(end_date, '%Y-%m-%d')
            except ValueError:
                return safe_json_dumps({"error": "Invalid date format. Use YYYY-MM-DD"}, indent=2)

        # Calculate date range for APIs that need it
        if use_date_range:
            from_date = start_date
            to_date = end_date
        else:
            # Convert period to date range
            period_days = {"1d": 1, "5d": 5, "1mo": 30, "3mo": 90, "6mo": 180, "1y": 365, "2y": 730, "5y": 1825}
            days = period_days.get(period, 365)
            to_date = datetime.now().strftime('%Y-%m-%d')
            from_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')

        # Yahoo Finance - Primary source
        try:
            yf_ticker = yf.Ticker(ticker)
            if use_date_range:
                hist = yf_ticker.history(start=start_date, end=end_date, interval=interval)
            else:
                hist = yf_ticker.history(period=period, interval=interval)

            if not hist.empty:
                # Convert to standardized format
                history_records = []
                for date, row in hist.iterrows():
                    # Handle date formatting safely
                    try:
                        date_str = str(date)[:10]  # Take first 10 chars for YYYY-MM-DD format
                    except:
                        date_str = str(date)

                    # Handle volume safely
                    volume = 0
                    try:
                        if pd and pd.isna(row['Volume']):
                            volume = 0
                        else:
                            volume = int(row['Volume'])
                    except:
                        volume = 0

                    history_records.append({
                        "date": date_str,
                        "open": float(row['Open']),
                        "high": float(row['High']),
                        "low": float(row['Low']),
                        "close": float(row['Close']),
                        "volume": volume
                    })

                # Calculate statistics
                latest_price = float(hist['Close'].iloc[-1])
                price_change = float(hist['Close'].iloc[-1] - hist['Close'].iloc[0]) if len(hist) > 1 else 0
                percent_change = float((price_change / hist['Close'].iloc[0] * 100)) if len(hist) > 1 and hist['Close'].iloc[0] != 0 else 0

                sources["yahoo_finance"] = {
                    "status": "success",
                    "data_points": len(history_records),
                    "latest_price": latest_price,
                    "price_change": round(price_change, 2),
                    "percent_change": round(percent_change, 2),
                    "high_period": float(hist['High'].max()),
                    "low_period": float(hist['Low'].min()),
                    "avg_volume": float(hist['Volume'].mean()),
                    "data": history_records
                }
            else:
                sources["yahoo_finance"] = {"status": "failed", "error": "No historical data found"}
        except Exception as e:
            sources["yahoo_finance"] = {"status": "failed", "error": str(e)}

        # Finnhub - Secondary source
        try:
            if finnhub_client:
                # Finnhub provides candle data for historical prices
                # Convert period to timestamp for Finnhub API
                to_timestamp = int(datetime.strptime(to_date, '%Y-%m-%d').timestamp()) if to_date else int(datetime.now().timestamp())
                from_timestamp = int(datetime.strptime(from_date, '%Y-%m-%d').timestamp()) if from_date else int((datetime.now() - timedelta(days=365)).timestamp())

                # Map interval to Finnhub resolution
                resolution_map = {
                    "1d": "D", "5d": "D", "1wk": "W", "1mo": "M",
                    "1m": "1", "5m": "5", "15m": "15", "30m": "30", "60m": "60"
                }
                resolution = resolution_map.get(interval, "D")

                candles = finnhub_client.stock_candles(ticker, resolution, from_timestamp, to_timestamp)

                if candles.get('s') == 'ok' and candles.get('c'):
                    # Convert Finnhub format to standardized format
                    finnhub_records = []
                    for i in range(len(candles['c'])):
                        date_str = datetime.fromtimestamp(candles['t'][i]).strftime('%Y-%m-%d')
                        finnhub_records.append({
                            "date": date_str,
                            "open": float(candles['o'][i]),
                            "high": float(candles['h'][i]),
                            "low": float(candles['l'][i]),
                            "close": float(candles['c'][i]),
                            "volume": int(candles['v'][i]) if candles.get('v') else 0
                        })

                    sources["finnhub"] = {
                        "status": "success",
                        "data_points": len(finnhub_records),
                        "data": finnhub_records
                    }
                else:
                    sources["finnhub"] = {"status": "failed", "error": "No candle data available"}
            else:
                # Generate demo historical data for Finnhub
                sources["finnhub"] = generate_demo_data(ticker, "historical", "finnhub", period=period, interval=interval, from_date=from_date, to_date=to_date)
        except Exception as e:
            sources["finnhub"] = {"status": "failed", "error": str(e)}

        # Financial Datasets - Third source
        try:
            if FINANCIAL_DATASETS_API_KEY:
                # Map interval to Financial Datasets format
                fd_interval = "day"
                if interval in ['1wk']: fd_interval = 'week'
                elif interval in ['1mo', '3mo']: fd_interval = 'month'

                params = {
                    "ticker": ticker,
                    "start_date": from_date,
                    "end_date": to_date,
                    "interval": fd_interval
                }

                fd_result = await call_financial_datasets_api("prices/historical", params)

                if "error" not in fd_result and fd_result.get("prices"):
                    # Convert to standardized format
                    fd_records = []
                    for price_data in fd_result["prices"]:
                        fd_records.append({
                            "date": price_data.get("date"),
                            "open": float(price_data.get("open", 0)),
                            "high": float(price_data.get("high", 0)),
                            "low": float(price_data.get("low", 0)),
                            "close": float(price_data.get("close", 0)),
                            "volume": int(price_data.get("volume", 0))
                        })

                    sources["financial_datasets"] = {
                        "status": "success",
                        "data_points": len(fd_records),
                        "data": fd_records
                    }
                else:
                    sources["financial_datasets"] = {"status": "failed", "error": fd_result.get("error", "No historical data")}
            else:
                # Generate demo historical data for Financial Datasets
                sources["financial_datasets"] = generate_demo_data(ticker, "historical", "financial_datasets", period=period, interval=interval, from_date=from_date, to_date=to_date)
        except Exception as e:
            sources["financial_datasets"] = {"status": "failed", "error": str(e)}

        # Stockdex - Fourth source
        try:
            if STOCKDEX_AVAILABLE:
                stockdex_result = call_stockdex_api(ticker, "historical", period=period, interval=interval, from_date=from_date, to_date=to_date)
                if "error" not in stockdex_result and stockdex_result.get("data"):
                    sources["stockdex"] = {
                        "status": "success",
                        "data_points": len(stockdex_result["data"]),
                        "data": stockdex_result["data"]
                    }
                else:
                    sources["stockdex"] = {"status": "failed", "error": stockdex_result.get("error", "No historical data")}
            else:
                # Generate demo historical data for Stockdex
                sources["stockdex"] = generate_demo_data(ticker, "historical", "stockdex", period=period, interval=interval, from_date=from_date, to_date=to_date)
        except Exception as e:
            sources["stockdex"] = {"status": "failed", "error": str(e)}

        # Determine primary data source and build result
        primary_source = None
        primary_data = None

        # Priority: Yahoo Finance > Finnhub > Financial Datasets
        if sources.get("yahoo_finance", {}).get("status") == "success":
            primary_source = "yahoo_finance"
            primary_data = sources["yahoo_finance"]["data"]
        elif sources.get("finnhub", {}).get("status") == "success":
            primary_source = "finnhub"
            primary_data = sources["finnhub"]["data"]
        elif sources.get("financial_datasets", {}).get("status") == "success":
            primary_source = "financial_datasets"
            primary_data = sources["financial_datasets"]["data"]

        if not primary_source:
            return safe_json_dumps({
                "error": "No historical data available from any source",
                "sources": {name: {"status": data.get("status"), "error": data.get("error")}
                           for name, data in sources.items()},
                "attempted_sources": list(sources.keys())
            }, indent=2)

        # Build enhanced multi-source result
        result = {
            "symbol": ticker,
            "timestamp": datetime.now().isoformat(),
            "primary_source": primary_source,
            "data_points": len(primary_data) if primary_data else 0,
            "sources": {
                name: {
                    "status": data.get("status"),
                    "data_points": data.get("data_points", 0),
                    "error": data.get("error") if data.get("status") == "failed" else None
                }
                for name, data in sources.items()
            },
            "summary": {
                "successful_sources": len([s for s in sources.values() if s.get("status") == "success"]),
                "total_sources": len(sources),
                "success_rate": f"{len([s for s in sources.values() if s.get('status') == 'success'])}/{len(sources)}",
                "multi_source_enabled": True
            }
        }

        # Add period or date range info
        if use_date_range:
            result.update({
                "start_date": start_date,
                "end_date": end_date,
                "interval": interval
            })
        else:
            result.update({
                "period": period,
                "interval": interval
            })

        # Add statistics from Yahoo Finance if available (most reliable for stats)
        if sources.get("yahoo_finance", {}).get("status") == "success":
            yf_data = sources["yahoo_finance"]
            result.update({
                "latest_price": yf_data.get("latest_price"),
                "price_change": yf_data.get("price_change"),
                "percent_change": yf_data.get("percent_change"),
                "high_period": yf_data.get("high_period"),
                "low_period": yf_data.get("low_period"),
                "avg_volume": yf_data.get("avg_volume")
            })

        # Add the historical data
        result["history"] = primary_data

        return safe_json_dumps(result, indent=2)
    except Exception as e:
        return safe_json_dumps({"error": str(e)}, indent=2)


@mcp.tool()
async def get_company_news(ticker: str, days: int = 7) -> str:
    """Get recent company news from multiple sources.

    Args:
        ticker: Stock symbol (e.g., 'AAPL', 'MSFT', 'GOOGL')
        days: Number of days to look back for news (default: 7)
    """
    try:
        # Use multi-source data function for news
        result = await get_multi_source_data(ticker, "news", days=days)

        # Collect all news articles from all sources
        all_news = []
        for source_name, source_data in result["sources"].items():
            if source_data.get("status") == "success" and source_data.get("data", {}).get("news"):
                all_news.extend(source_data["data"]["news"])

        # Deduplicate news articles
        unique_news = []
        seen_urls = set()
        seen_headlines = set()

        for article in all_news:
            url = article.get("url", "")
            headline = article.get("headline", "")

            # Skip if we've seen this URL or very similar headline
            if url and url in seen_urls:
                continue
            if headline and headline in seen_headlines:
                continue

            unique_news.append(article)
            if url:
                seen_urls.add(url)
            if headline:
                seen_headlines.add(headline)

        # Build enhanced result with multi-source information
        enhanced_result = {
            "symbol": result["symbol"],
            "timestamp": result["timestamp"],
            "days_requested": days,
            "total_articles": len(unique_news),
            "sources": {name: {"status": data["status"], "articles": data.get("articles", 0)}
                       for name, data in result["sources"].items()},
            "summary": result["summary"],
            "news": unique_news[:20]  # Limit to 20 articles
        }

        return safe_json_dumps(enhanced_result, indent=2)
    except Exception as e:
        return safe_json_dumps({"error": str(e)}, indent=2)


@mcp.tool()
async def get_financial_statements(
    ticker: str,
    statement_type: str = "income",
    period: str = "annual",
    limit: int = 4
) -> str:
    """Get financial statements from multiple sources.

    Args:
        ticker: Stock symbol (e.g., 'AAPL', 'MSFT', 'GOOGL')
        statement_type: Type of statement ('income', 'balance', 'cash_flow')
        period: Period type ('annual', 'quarterly')
        limit: Number of periods to return (default: 4)
    """
    try:
        # Use multi-source data function for financial statements
        result = await get_multi_source_data(
            ticker,
            "financials",
            statement_type=statement_type,
            period=period,
            limit=limit
        )

        # Get Yahoo Finance data as primary/fallback
        try:
            ticker = ticker.upper()
            yf_ticker = yf.Ticker(ticker)

            if statement_type == "income":
                if period == "annual":
                    statements = yf_ticker.financials
                else:
                    statements = yf_ticker.quarterly_financials
            elif statement_type == "balance":
                if period == "annual":
                    statements = yf_ticker.balance_sheet
                else:
                    statements = yf_ticker.quarterly_balance_sheet
            elif statement_type == "cash_flow":
                if period == "annual":
                    statements = yf_ticker.cashflow
                else:
                    statements = yf_ticker.quarterly_cashflow
            else:
                return safe_json_dumps({"error": "Invalid statement_type. Use 'income', 'balance', or 'cash_flow'"}, indent=2)

            if not statements.empty:
                # Convert to records and limit with proper timestamp handling
                try:
                    # Safely limit the number of columns
                    actual_limit = min(limit, len(statements.columns))
                    statements_limited = statements.iloc[:, :actual_limit].copy()

                    # Convert column names (timestamps) to strings safely
                    new_columns = []
                    for col in statements_limited.columns:
                        try:
                            # Try to format as date if it's a timestamp
                            formatted_col = getattr(col, 'strftime', lambda _: str(col))('%Y-%m-%d')
                            new_columns.append(formatted_col)
                        except:
                            new_columns.append(str(col))
                    statements_limited.columns = new_columns

                    # Convert index (row names) to strings as well - safer approach
                    statements_limited.index = statements_limited.index.astype(str)

                    # Convert to dictionary with error handling
                    # Use 'columns' orientation for more intuitive structure (periods as keys)
                    statements_data = statements_limited.to_dict(orient="dict")

                    result["sources"]["yahoo_finance"] = {
                        "status": "success",
                        "data": {
                            "statements": statements_data,
                            "periods": len(statements_data),
                            "statement_type": statement_type
                        }
                    }

                except Exception:
                    # Fallback: create a simpler structure
                    statements_data = {}
                    actual_limit = min(limit, len(statements.columns))

                    for i, col in enumerate(statements.columns[:actual_limit]):
                        col_name = str(col)
                        if hasattr(col, 'strftime'):
                            try:
                                col_name = getattr(col, 'strftime')('%Y-%m-%d')
                            except:
                                col_name = str(col)

                        statements_data[col_name] = {}
                        for idx, value in statements.iloc[:, i].items():
                            statements_data[col_name][str(idx)] = value

                    result["sources"]["yahoo_finance"] = {
                        "status": "success",
                        "data": {
                            "statements": statements_data,
                            "periods": len(statements_data),
                            "statement_type": statement_type
                        }
                    }
            else:
                result["sources"]["yahoo_finance"] = {"status": "failed", "error": f"No {statement_type} statements found"}
        except Exception as e:
            result["sources"]["yahoo_finance"] = {"status": "failed", "error": str(e)}

        # Determine primary data source and build enhanced result
        primary_source = None
        primary_data = None

        if result["sources"].get("financial_datasets", {}).get("status") == "success":
            primary_source = "financial_datasets"
            primary_data = result["sources"]["financial_datasets"]["data"]
        elif result["sources"].get("yahoo_finance", {}).get("status") == "success":
            primary_source = "yahoo_finance"
            primary_data = result["sources"]["yahoo_finance"]["data"]

        if not primary_source:
            return safe_json_dumps({
                "error": "No financial statements available from any source",
                "sources": result["sources"]
            }, indent=2)

        # Build enhanced result with multi-source information
        enhanced_result = {
            "symbol": result["symbol"],
            "statement_type": statement_type,
            "period": period,
            "timestamp": result["timestamp"],
            "primary_source": primary_source,
            "periods_count": primary_data.get("periods", 0) if primary_data else 0,
            "statements": primary_data.get("statements", {}) if primary_data else {},
            "sources": {name: {"status": data["status"], "periods": data.get("data", {}).get("periods", 0) if data.get("status") == "success" else 0}
                       for name, data in result["sources"].items()},
            "summary": result["summary"]
        }

        return safe_json_dumps(enhanced_result, indent=2)
    except Exception as e:
        return safe_json_dumps({"error": str(e)}, indent=2)


@mcp.tool()
async def get_sec_filings(
    ticker: str,
    form_type: str = "10-K",
    limit: int = 5
) -> str:
    """Get SEC filings for a company.

    Args:
        ticker: Stock symbol (e.g., 'AAPL', 'MSFT', 'GOOGL')
        form_type: Type of SEC form ('10-K', '10-Q', '8-K', 'DEF 14A')
        limit: Number of filings to return (default: 5)
    """
    try:
        if not SEC_EDGAR_AVAILABLE:
            return safe_json_dumps({"error": "sec-edgar-api package not installed. Install with: pip install sec-edgar-api"}, indent=2)

        ticker = ticker.upper()

        # Initialize Edgar client with error handling
        if not EdgarClient:
            return safe_json_dumps({"error": "EdgarClient not available"}, indent=2)

        edgar = EdgarClient(user_agent="Multi-Source Finance MCP/1.0")

        # Common ticker to CIK mappings for major companies
        ticker_to_cik = {
            "AAPL": "0000320193",
            "MSFT": "0000789019",
            "GOOGL": "0001652044",
            "GOOG": "0001652044",
            "AMZN": "0001018724",
            "TSLA": "0001318605",
            "META": "0001326801",
            "FB": "0001326801",
            "NVDA": "0001045810",
            "BRK.A": "0001067983",
            "BRK.B": "0001067983",
            "JPM": "0000019617",
            "JNJ": "0000200406",
            "V": "0001403161",
            "PG": "0000080424",
            "UNH": "0000731766",
            "HD": "0000354950",
            "MA": "0001141391",
            "DIS": "0001001039",
            "PYPL": "0001633917",
            "BAC": "0000070858",
            "ADBE": "0000796343",
            "NFLX": "0001065280",
            "CRM": "0001108524",
            "XOM": "0000034088",
            "KO": "0000021344",
            "PFE": "0000078003",
            "INTC": "0000050863",
            "VZ": "0000732712",
            "CMCSA": "0001166691",
            "T": "0000732717",
            "WMT": "0000104169",
            "CVX": "0000093410"
        }

        # Convert ticker to CIK if available
        cik = ticker_to_cik.get(ticker, ticker)

        # If ticker wasn't in our mapping and doesn't look like a CIK, try to find it
        if cik == ticker and not cik.isdigit():
            # For unknown tickers, we'll try the ticker as-is and provide helpful error
            pass

        # Get company submissions (which contains filings information)
        try:
            # Use get_submissions method which is available in the API
            submissions_response = edgar.get_submissions(cik=cik)

            # Extract company information and filings
            company_name = "Unknown Company"
            cik = ticker
            filings = []

            if isinstance(submissions_response, dict):
                # Extract company info
                company_name = submissions_response.get("name", company_name)
                cik = submissions_response.get("cik", cik)

                # Extract recent filings
                recent_filings = submissions_response.get("filings", {}).get("recent", {})
                if recent_filings:
                    forms = recent_filings.get("form", [])
                    filing_dates = recent_filings.get("filingDate", [])
                    accession_numbers = recent_filings.get("accessionNumber", [])

                    # Filter by form type and create filings list
                    for i, form in enumerate(forms):
                        if form == form_type and len(filings) < limit:
                            filing_date = filing_dates[i] if i < len(filing_dates) else "Unknown"
                            accession = accession_numbers[i] if i < len(accession_numbers) else "Unknown"

                            filings.append({
                                "form": form,
                                "filing_date": filing_date,
                                "accession_number": accession,
                                "description": f"{form} filing for {company_name}"
                            })

        except Exception as api_error:
            error_msg = str(api_error)
            if "Invalid CIK" in error_msg and cik == ticker:
                return safe_json_dumps({
                    "error": f"Ticker '{ticker}' not found in SEC database. Supported tickers include: AAPL, MSFT, GOOGL, AMZN, TSLA, META, NVDA, JPM, etc. You can also use a 10-digit CIK number directly."
                }, indent=2)
            else:
                return safe_json_dumps({"error": f"SEC EDGAR API error: {error_msg}"}, indent=2)

        if not filings:
            return safe_json_dumps({"error": f"No {form_type} filings found for {ticker}"}, indent=2)

        result = {
            "symbol": ticker,
            "company_name": company_name,
            "cik": cik,
            "form_type": form_type,
            "timestamp": datetime.now().isoformat(),
            "filings_count": len(filings),
            "filings": filings,
            "source": "sec_edgar"
        }

        return safe_json_dumps(result, indent=2)
    except Exception as e:
        return safe_json_dumps({"error": str(e)}, indent=2)



@mcp.tool()
async def compare_stocks(tickers: str, period: str = "1y") -> str:
    """Compare multiple stocks performance.

    Args:
        tickers: Comma-separated stock symbols (e.g., 'AAPL,MSFT,GOOGL')
        period: Time period for comparison (1d, 5d, 1mo, 3mo, 6mo, 1y, 2y, 5y, 10y, ytd, max)
    """
    try:
        ticker_list = [t.strip().upper() for t in tickers.split(',')]
        comparison_data = {}

        sources_summary = {}

        for ticker in ticker_list:
            try:
                # Get multi-source quote data for current price
                quote_result = await get_multi_source_data(ticker, "quote")

                # Get historical data for performance calculation
                yf_ticker = yf.Ticker(ticker)
                hist = yf_ticker.history(period=period)

                if not hist.empty and quote_result["sources"]:
                    start_price = float(hist['Close'].iloc[0])

                    # Use consensus price from multi-source data
                    current_prices = []
                    for source_data in quote_result["sources"].values():
                        if source_data.get("status") == "success" and source_data.get("data", {}).get("price"):
                            current_prices.append(source_data["data"]["price"])

                    end_price = sum(current_prices) / len(current_prices) if current_prices else float(hist['Close'].iloc[-1])
                    price_change = end_price - start_price
                    percent_change = (price_change / start_price * 100) if start_price != 0 else 0

                    # Get company info from multi-source
                    info_result = await get_multi_source_data(ticker, "info")
                    company_name = ticker
                    for source_data in info_result["sources"].values():
                        if source_data.get("status") == "success" and source_data.get("data", {}).get("company_name"):
                            company_name = source_data["data"]["company_name"]
                            break

                    comparison_data[ticker] = {
                        "company_name": company_name,
                        "start_price": round(start_price, 2),
                        "end_price": round(end_price, 2),
                        "price_change": round(price_change, 2),
                        "percent_change": round(percent_change, 2),
                        "data_sources": len([s for s in quote_result["sources"].values() if s.get("status") == "success"]),
                        "total_sources": len(quote_result["sources"])
                    }

                    # Track source performance
                    for source_name, source_data in quote_result["sources"].items():
                        if source_name not in sources_summary:
                            sources_summary[source_name] = {"success": 0, "total": 0}
                        sources_summary[source_name]["total"] += 1
                        if source_data.get("status") == "success":
                            sources_summary[source_name]["success"] += 1
                else:
                    comparison_data[ticker] = {"error": "No data available"}
            except Exception as e:
                comparison_data[ticker] = {"error": str(e)}

        # Sort by performance
        valid_stocks = {k: v for k, v in comparison_data.items() if "error" not in v}
        sorted_stocks = sorted(valid_stocks.items(), key=lambda x: x[1]['percent_change'], reverse=True)

        result = {
            "period": period,
            "timestamp": datetime.now().isoformat(),
            "stocks_compared": len(ticker_list),
            "successful_comparisons": len(valid_stocks),
            "comparison": dict(sorted_stocks),
            "best_performer": sorted_stocks[0][0] if sorted_stocks else None,
            "worst_performer": sorted_stocks[-1][0] if sorted_stocks else None,
            "sources_performance": {
                name: f"{data['success']}/{data['total']}"
                for name, data in sources_summary.items()
            },
            "multi_source_enabled": True
        }

        return safe_json_dumps(result, indent=2)
    except Exception as e:
        return safe_json_dumps({"error": str(e)}, indent=2)


if __name__ == "__main__":
    logger.info("Starting Multi-Source Finance MCP Server...")
    mcp.run(transport="stdio")
    logger.info("Server stopped")
